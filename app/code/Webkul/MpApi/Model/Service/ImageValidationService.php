<?php
/**
 * Webkul Software.
 *
 * @category   Webkul
 * @package    Webkul_MpApi
 * <AUTHOR> Software Private Limited
 * @copyright  Webkul Software Private Limited (https://webkul.com)
 * @license    https://store.webkul.com/license.html
 */
declare(strict_types=1);

namespace Webkul\MpApi\Model\Service;

use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\App\RequestInterface;

/**
 * Service class for validating image uploads
 */
class ImageValidationService
{
    /**
     * @var ImageUploadConfig
     */
    private $config;

    /**
     * @var RequestInterface
     */
    private $request;

    /**
     * Constructor
     *
     * @param ImageUploadConfig $config
     * @param RequestInterface $request
     */
    public function __construct(
        ImageUploadConfig $config,
        RequestInterface $request
    ) {
        $this->config = $config;
        $this->request = $request;
    }

    /**
     * Validate upload request and extract images
     *
     * @return array
     * @throws LocalizedException
     */
    public function validateUploadRequest(): array
    {
        $files = $this->getUploadedFiles();
        
        if ($this->request->getMethod() !== "POST" || empty($files)) {
            throw new LocalizedException(__("Invalid Request."));
        }

        if (!isset($files['images']) || !is_array($files['images'])) {
            throw new LocalizedException(__("No images found in request."));
        }

        $images = $files['images'];
        $imageCount = count($images);

        if ($imageCount < $this->config->getMinImageCount() || $imageCount > $this->config->getMaxImageCount()) {
            throw new LocalizedException(
                __('Please upload between %1 and %2 images.', 
                   $this->config->getMinImageCount(), 
                   $this->config->getMaxImageCount())
            );
        }

        return $images;
    }

    /**
     * Validate all image files
     *
     * @param array $images
     * @throws LocalizedException
     */
    public function validateImageFiles(array $images): void
    {
        $totalSize = 0;
        $maxTotalSize = $this->config->getMaxTotalFileSize();

        foreach ($images as $index => $image) {
            $this->validateSingleImage($image, $index);
            $totalSize += $image['size'];
        }

        if ($totalSize > $maxTotalSize) {
            throw new LocalizedException(
                __('Total file size exceeds maximum limit of %1MB.',
                   round($maxTotalSize / $this->config->getBytesToMbFactor()))
            );
        }
    }

    /**
     * Validate a single image file
     *
     * @param array $image
     * @param int $index
     * @throws LocalizedException
     */
    public function validateSingleImage(array $image, int $index): void
    {
        $allowedExtensions = $this->config->getAllowedExtensions();
        $maxIndividualSize = $this->config->getMaxIndividualFileSize();

        if (!isset($image['error']) || $image['error'] !== UPLOAD_ERR_OK) {
            throw new LocalizedException(__('Upload error for image %1.', $index + 1));
        }

        if (!isset($image['size']) || $image['size'] > $maxIndividualSize) {
            throw new LocalizedException(
                __('Image %1 exceeds maximum size of %2MB.',
                   $index + 1,
                   round($maxIndividualSize / $this->config->getBytesToMbFactor()))
            );
        }

        if (!isset($image['name'])) {
            throw new LocalizedException(__('Missing filename for image %1.', $index + 1));
        }

        $fileName = $image['name'];
        $fileExtension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

        if (!in_array($fileExtension, $allowedExtensions)) {
            throw new LocalizedException(
                __('Invalid file type for image %1. Allowed types: %2', 
                   $index + 1, 
                   implode(', ', $allowedExtensions))
            );
        }

        if (!isset($image['tmp_name']) || !$this->isValidImageFile($image['tmp_name'])) {
            throw new LocalizedException(__('File %1 is not a valid image.', $index + 1));
        }
    }

    /**
     * Check if uploaded file is a valid image
     *
     * @param string $tmpName
     * @return bool
     */
    public function isValidImageFile(string $tmpName): bool
    {
        if (!file_exists($tmpName)) {
            return false;
        }

        $imageInfo = getimagesize($tmpName);
        return $imageInfo !== false;
    }

    /**
     * Get uploaded files from request
     * This method encapsulates access to $_FILES superglobal
     *
     * @return array
     */
    private function getUploadedFiles(): array
    {
        return $this->request->getFiles()->toArray();
    }
}
