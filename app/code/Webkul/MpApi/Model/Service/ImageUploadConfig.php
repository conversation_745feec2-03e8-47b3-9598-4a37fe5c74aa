<?php
/**
 * Webkul Software.
 *
 * @category   Webkul
 * @package    Webkul_MpApi
 * <AUTHOR> Software Private Limited
 * @copyright  Webkul Software Private Limited (https://webkul.com)
 * @license    https://store.webkul.com/license.html
 */
declare(strict_types=1);

namespace Webkul\MpApi\Model\Service;

/**
 * Configuration class for image upload settings
 */
class ImageUploadConfig
{
    /**
     * Allowed image file extensions
     */
    public const ALLOWED_EXTENSIONS = [
        'jpeg', 'jpg', 'png', 'gif', 'webp', 'svg', 'avif', 'jfif'
    ];

    /**
     * Allowed image file extensions with case variations for uploader
     */
    public const ALLOWED_EXTENSIONS_UPLOADER = [
        'jpeg', 'jpg', 'png', 'gif', 'JPEG', 'JPG', 'PNG', 'GIF',
        'webp', 'WEBP', 'svg', 'SVG', 'avif', 'AVIF', 'jfif', 'JFIF'
    ];

    /**
     * Maximum individual file size in bytes (5MB)
     */
    public const MAX_INDIVIDUAL_FILE_SIZE = 5 * 1024 * 1024;

    /**
     * Maximum total file size in bytes (20MB)
     */
    public const MAX_TOTAL_FILE_SIZE = 20 * 1024 * 1024;

    /**
     * Minimum number of images allowed
     */
    public const MIN_IMAGE_COUNT = 1;

    /**
     * Maximum number of images allowed
     */
    public const MAX_IMAGE_COUNT = 4;

    /**
     * Temporary file prefix for processing
     */
    public const TEMP_FILE_PREFIX = 'img_';

    /**
     * Temporary file suffix for uploaded files
     */
    public const TEMP_FILE_SUFFIX = '.tmp';

    /**
     * Get allowed extensions for validation
     *
     * @return array
     */
    public function getAllowedExtensions(): array
    {
        return self::ALLOWED_EXTENSIONS;
    }

    /**
     * Get allowed extensions for file uploader
     *
     * @return array
     */
    public function getAllowedExtensionsForUploader(): array
    {
        return self::ALLOWED_EXTENSIONS_UPLOADER;
    }

    /**
     * Get maximum individual file size
     *
     * @return int
     */
    public function getMaxIndividualFileSize(): int
    {
        return self::MAX_INDIVIDUAL_FILE_SIZE;
    }

    /**
     * Get maximum total file size
     *
     * @return int
     */
    public function getMaxTotalFileSize(): int
    {
        return self::MAX_TOTAL_FILE_SIZE;
    }

    /**
     * Get minimum image count
     *
     * @return int
     */
    public function getMinImageCount(): int
    {
        return self::MIN_IMAGE_COUNT;
    }

    /**
     * Get maximum image count
     *
     * @return int
     */
    public function getMaxImageCount(): int
    {
        return self::MAX_IMAGE_COUNT;
    }

    /**
     * Get temporary file prefix
     *
     * @return string
     */
    public function getTempFilePrefix(): string
    {
        return self::TEMP_FILE_PREFIX;
    }

    /**
     * Get temporary file suffix
     *
     * @return string
     */
    public function getTempFileSuffix(): string
    {
        return self::TEMP_FILE_SUFFIX;
    }
}
